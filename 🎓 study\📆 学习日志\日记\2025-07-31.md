---
date: 2025-07-31
area: 学习
tags:
  - 日程
aliases:
  - 2025年07月31日
cssclasses:
  - daily-page
status: Todo
---
# 📅 今日学习计划：2025-07-31 星期四

## ✅ 今日学习任务
- [ ] 英语学习  🔁 every day ⏫ 


### 🎯 手动添加的任务
- [ ] #task 高等数学习题练习 📅 2025-07-31 ⏫ 🔄 every day
- [ ] #task 英语单词记忆 📅 2025-07-31 📌

### 📌 进行中的项目概览

```dataviewjs
// 显示所有进行中的项目及其任务
const projectPages = dv.pages('"🎓 study/📌 学习项目"')
  .where(p => p.状态 === "进行中" || p.状态 === "待办")
  .where(p => p.file.name !== "项目管理看板");

if (projectPages.length === 0) {
  dv.paragraph("📝 当前没有进行中的项目");
} else {
  for (let project of projectPages) {
    // 项目标题
    dv.header(4, `🎯 [[${project.file.name}|${project.file.name}]] (${project.状态 || "进行中"})`);

    // 获取项目任务
    const projectTasks = project.file.tasks.where(t => !t.completed);

    if (projectTasks.length > 0) {
      // 按优先级分组显示任务
      const highPriorityTasks = projectTasks.where(t =>
        t.text.includes("⏫") || t.text.includes("🔥")
      );
      const normalTasks = projectTasks.where(t =>
        !t.text.includes("⏫") && !t.text.includes("🔥")
      );

      if (highPriorityTasks.length > 0) {
        dv.paragraph("**🔥 高优先级任务：**");
        for (let task of highPriorityTasks) {
          dv.paragraph(`- ${task.text.replace(/^- \[ \] /, '').replace(/#task\/?[^\s]*\s*/g, '').trim()}`);
        }
      }

      if (normalTasks.length > 0) {
        dv.paragraph("**📌 普通任务：**");
        for (let task of normalTasks.slice(0, 3)) { // 只显示前3个
          dv.paragraph(`- ${task.text.replace(/^- \[ \] /, '').replace(/#task\/?[^\s]*\s*/g, '').trim()}`);
        }
        if (normalTasks.length > 3) {
          dv.paragraph(`- ... 还有 ${normalTasks.length - 3} 个任务`);
        }
      }
    } else {
      dv.paragraph("📝 暂无未完成任务");
    }

    dv.paragraph("---");
  }

  // 添加快速操作提示
  dv.paragraph("💡 **快速操作：**");
  dv.paragraph("- 使用 QuickAdd 命令 `📌 添加项目任务到今日` 快速添加项目任务");
  dv.paragraph("- 或者直接复制上面的任务描述到下方的手动任务中");
}
```

### 🔥 今日重点任务推荐

```dataviewjs
// 基于项目优先级和截止日期推荐今日任务
const today = new Date().toISOString().slice(0, 10);
const projectPages = dv.pages('"🎓 study/📌 学习项目"')
  .where(p => p.状态 === "进行中" || p.状态 === "待办")
  .where(p => p.file.name !== "项目管理看板");

let recommendedTasks = [];

for (let project of projectPages) {
  const tasks = project.file.tasks.where(t => !t.completed);

  for (let task of tasks) {
    let priority = 0;

    // 优先级评分
    if (task.text.includes("⏫")) priority += 3;
    if (task.text.includes("🔥")) priority += 2;
    if (task.due && task.due <= today) priority += 2; // 今天或过期
    if (task.due && task.due <= new Date(Date.now() + 3*24*60*60*1000).toISOString().slice(0, 10)) priority += 1; // 3天内

    if (priority > 0) {
      recommendedTasks.push({
        project: project.file.name,
        task: task.text.replace(/^- \[ \] /, '').replace(/#task\/?[^\s]*\s*/g, '').trim(),
        priority: priority,
        due: task.due || "无截止日期"
      });
    }
  }
}

// 按优先级排序
recommendedTasks.sort((a, b) => b.priority - a.priority);

if (recommendedTasks.length > 0) {
  dv.header(4, "🎯 建议今日完成的任务");
  dv.table(
    ["项目", "任务", "截止日期", "优先级"],
    recommendedTasks.slice(0, 5).map(t => [
      `[[${t.project}]]`,
      t.task,
      t.due,
      "⭐".repeat(t.priority)
    ])
  );
} else {
  dv.paragraph("✅ 当前没有紧急任务，可以自由安排学习内容");
}
```

## 📌 今日学习安排


## 📌 事件日志






## 📊 今日学习时长统计

```dataviewjs
// 自动计算当前文件的学习时长
const currentFile = dv.current();
const content = await dv.io.load(currentFile.file.path);
const lines = content.split('\n');

let totalMinutes = 0;
let sessionCount = 0;
let sessions = [];

for (let i = 0; i < lines.length; i++) {
  const line = lines[i];
  if (line.includes('**时间段：**')) {
    const minuteMatch = line.match(/(\d+)min/);
    const timeMatch = line.match(/`([^`]+)`/);

    if (minuteMatch && timeMatch) {
      const minutes = parseInt(minuteMatch[1]);
      const timeRange = timeMatch[1];
      totalMinutes += minutes;
      sessionCount++;

      // 获取活动内容
      let activity = '';
      for (let j = i + 1; j < Math.min(i + 8, lines.length); j++) {
        if (lines[j].includes('**做了什么：**')) {
          for (let k = j + 1; k < lines.length; k++) {
            if (lines[k].includes('**当时想法与感悟：**')) break;
            if (lines[k].trim().startsWith('- ') || lines[k].trim().startsWith('\t- ')) {
              activity += lines[k].trim().replace(/^- /, '') + ' ';
            }
          }
          break;
        }
      }

      sessions.push([timeRange, `${minutes}分钟`, activity.trim()]);
    }
  }
}

const hours = Math.floor(totalMinutes / 60);
const remainingMinutes = totalMinutes % 60;
const totalTimeStr = hours > 0 ? `${hours}小时${remainingMinutes}分钟` : `${remainingMinutes}分钟`;

// 显示统计信息
dv.header(3, "📈 学习时长汇总");
dv.paragraph(`**总学习时长：** ${totalTimeStr} (${totalMinutes}分钟)`);
dv.paragraph(`**学习次数：** ${sessionCount}次`);
dv.paragraph(`**平均每次时长：** ${sessionCount > 0 ? Math.round(totalMinutes / sessionCount) : 0}分钟`);

if (sessions.length > 0) {
  dv.header(4, "📋 学习会话详情");
  dv.table(["时间段", "时长", "学习内容"], sessions);
}

// 学习效率分析
if (totalMinutes > 0) {
  dv.header(4, "📊 效率分析");
  const avgSessionLength = Math.round(totalMinutes / sessionCount);
  let efficiencyTip = "";

  if (avgSessionLength < 25) {
    efficiencyTip = "💡 建议：学习时间较短，可以尝试延长单次学习时间到25-50分钟";
  } else if (avgSessionLength > 90) {
    efficiencyTip = "💡 建议：单次学习时间较长，建议适当休息以保持专注力";
  } else {
    efficiencyTip = "✅ 很好：学习时间安排合理，保持这个节奏";
  }

  dv.paragraph(efficiencyTip);
}
```

## 📔 今日学习回顾

> 今日学习收获：

> 存在问题：

> 明日改进措施：